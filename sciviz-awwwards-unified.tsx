"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import * as THREE from 'three';
import {
  Menu, X, ArrowRight, Grid, Layers, Eye, Code, Download, Play, Pause, Maximize2,
  Search, Monitor, Tablet, Smartphone, Atom, Dna, Telescope, Brain, Infinity, Microscope, Layers3, Waves, BarChart3, List, CheckCircle, Activity, Cpu, Database, Network, GitBranch
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { twMerge } from 'tailwind-merge';
import { clsx, type ClassValue } from 'clsx';

// Utility for combining Tailwind classes
function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// --- DATA DEFINITIONS ---
const scientificLayouts = {
    'modular-grid': {
        name: 'Modular Grid',
        category: 'Grid Systems',
        description: 'Scientific data organized in flexible, responsive grid modules.',
        icon: Grid,
        performance: { speed: 95, accessibility: 98, mobile: 99 },
        features: ['Responsive Grid', 'Data Modules', 'Hover States'],
        lastUpdated: '2025-06-29'
    },
    'dashboard-panel': {
        name: 'Scientific Dashboard',
        category: 'Analytics',
        description: 'Real-time monitoring of experimental parameters and system status.',
        icon: BarChart3,
        performance: { speed: 91, accessibility: 94, mobile: 96 },
        features: ['Live Charts', 'Status Indicators', 'Data Widgets'],
        lastUpdated: '2025-06-28'
    },
    'floating-console': {
        name: 'Floating Console',
        category: 'Control Interfaces',
        description: 'A hovering control panel for complex scientific instruments and simulations.',
        icon: Layers,
        performance: { speed: 88, accessibility: 92, mobile: 91 },
        features: ['Floating UI', 'Instrument Control', 'Real-time Input'],
        lastUpdated: '2025-06-27'
    },
    'multi-column-cards': {
        name: 'Multi-Column Cards',
        category: 'Content Display',
        description: 'Research findings, articles, and data points presented in elegant card collections.',
        icon: List,
        performance: { speed: 96, accessibility: 97, mobile: 98 },
        features: ['Masonry Layout', 'Dynamic Content', 'Detailed Previews'],
        lastUpdated: '2025-06-26'
    },
    'neural-network': {
      name: 'Neural Network',
      category: 'AI & Machine Learning',
      description: 'Interconnected nodes representing AI neural pathways with dynamic data flow visualization.',
      icon: Brain,
      performance: { speed: 92, accessibility: 96, mobile: 98 },
      features: ['Dynamic Connections', 'Real-time Data Flow', 'Interactive Nodes'],
      lastUpdated: '2025-01-15'
    },
    'quantum-grid': {
      name: 'Quantum Grid',
      category: 'Scientific Computing',
      description: 'Quantum computing visualization with probability states and superposition effects.',
      icon: Atom,
      performance: { speed: 89, accessibility: 94, mobile: 96 },
      features: ['Quantum States', 'Probability Viz', 'Superposition Effects'],
      lastUpdated: '2025-01-12'
    },
};
type LayoutId = keyof typeof scientificLayouts;
type Layout = typeof scientificLayouts[LayoutId];

// Sample data for populating the detailed layout previews
const scientificData = [
    { id: 1, title: 'Quantum Entanglement', field: 'Physics', icon: Atom, progress: 87, status: 'active' },
    { id: 2, title: 'Neural Networks', field: 'AI/ML', icon: Brain, progress: 94, status: 'complete' },
    { id: 3, title: 'Genetic Sequencing', field: 'Biology', icon: Dna, progress: 76, status: 'active' },
    { id: 4, title: 'Stellar Formation', field: 'Astronomy', icon: Telescope, progress: 62, status: 'pending' },
    { id: 5, title: 'Protein Folding', field: 'Biochemistry', icon: Microscope, progress: 89, status: 'active' },
    { id: 6, title: 'Quantum Computing', field: 'Computing', icon: Cpu, progress: 73, status: 'active' }
];


// --- MAIN UNIFIED COMPONENT ---
const SciVizAwwwardsUnified = () => {
    // 3D Background State
    const mountRef = useRef<HTMLDivElement>(null);
    const animationRef = useRef<number>();
    const [isAnimationPaused, setIsAnimationPaused] = useState(false);
    const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
    const [scrollY, setScrollY] = useState(0);

    // UI State
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [selectedLayout, setSelectedLayout] = useState<LayoutId>('modular-grid');
    const [selectedDevice, setSelectedDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [searchQuery, setSearchQuery] = useState('');
    const [isPreviewFullscreen, setIsPreviewFullscreen] = useState(false);

    // --- 3D BACKGROUND LOGIC ---
    useEffect(() => {
        if (!mountRef.current) return;
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);
        mountRef.current.appendChild(renderer.domElement);
        const geometry = new THREE.IcosahedronGeometry(2.5, 1);
        const material = new THREE.MeshBasicMaterial({ color: 0xFF6600, wireframe: true, transparent: true, opacity: 0.6 });
        const mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);
        camera.position.z = 7;
        const animate = () => {
            animationRef.current = requestAnimationFrame(animate);
            if (!isAnimationPaused) {
                mesh.rotation.x += 0.001;
                mesh.rotation.y += 0.002;
                mesh.position.x = (mousePosition.x - 0.5) * 1.5;
                mesh.position.y = (mousePosition.y - 0.5) * -1.5;
                const scale = 1 + scrollY * 0.0002;
                mesh.scale.set(scale, scale, scale);
            }
            renderer.render(scene, camera);
        };
        animate();
        const handleResize = () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        };
        window.addEventListener('resize', handleResize);
        return () => {
            if (animationRef.current) cancelAnimationFrame(animationRef.current);
            if (mountRef.current && renderer.domElement) mountRef.current.removeChild(renderer.domElement);
            window.removeEventListener('resize', handleResize);
        };
    }, [isAnimationPaused, mousePosition, scrollY]);

    // --- EVENT LISTENERS ---
    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => setMousePosition({ x: e.clientX / window.innerWidth, y: e.clientY / window.innerHeight });
        const handleScroll = () => setScrollY(window.scrollY);
        const handleKeyDown = (e: KeyboardEvent) => e.key === 'Escape' && setIsPreviewFullscreen(false);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    // --- LAYOUT DEFINITIONS & RENDERERS ---
    
    const renderModularGrid = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 h-full overflow-y-auto bg-white">
            {scientificData.map((item) => (
                <div key={item.id} className="bg-white border-2 border-black p-4 group hover:bg-black hover:text-white transition-all duration-300 cursor-pointer rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                        <item.icon className="w-6 h-6 text-black group-hover:text-orange transition-colors" />
                        <div className={cn("w-2.5 h-2.5 rounded-full", item.status === 'complete' ? 'bg-orange' : item.status === 'active' ? 'bg-black group-hover:bg-white' : 'bg-black/30 group-hover:bg-white/30')} />
                    </div>
                    <h3 className="text-md font-bold mb-1">{item.title}</h3>
                    <p className="text-xs mb-3 opacity-70">{item.field}</p>
                    <div className="w-full bg-black/10 group-hover:bg-white/20 h-1.5 rounded-full">
                        <div className="h-full bg-orange rounded-full" style={{ width: `${item.progress}%` }} />
                    </div>
                    <div className="mt-2 text-xs opacity-70">{item.progress}% Complete</div>
                </div>
            ))}
        </div>
    );
    
    const renderDashboard = () => (
         <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 p-4 h-full overflow-y-auto bg-white">
            <div className="lg:col-span-2 space-y-4">
                <div className="bg-white border-2 border-black p-4 rounded-lg">
                    <h3 className="font-bold mb-3">Experimental Progress</h3>
                    <div className="h-40 bg-black/5 flex items-end justify-between p-2 rounded">
                        {[40, 60, 35, 80, 55, 90, 75, 85].map((height, index) => (
                            <div key={index} className="bg-orange w-6 transition-all duration-300 hover:bg-black" style={{ height: `${height}%` }} />
                        ))}
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white border-2 border-black p-4 rounded-lg">
                        <h4 className="font-bold text-sm mb-1">Active Experiments</h4>
                        <div className="text-3xl font-bold text-orange">24</div>
                    </div>
                    <div className="bg-white border-2 border-black p-4 rounded-lg">
                        <h4 className="font-bold text-sm mb-1">Success Rate</h4>
                        <div className="text-3xl font-bold text-orange">94.2%</div>
                    </div>
                </div>
            </div>
            <div className="space-y-4">
                <div className="bg-black text-white p-4 rounded-lg">
                    <h3 className="font-bold mb-3">System Status</h3>
                    <div className="space-y-2">
                        {['Quantum Processor', 'Neural Interface', 'Data Pipeline'].map((system) => (
                            <div key={system} className="flex items-center justify-between">
                                <span className="text-sm">{system}</span>
                                <div className="w-2.5 h-2.5 bg-orange rounded-full" />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );

    const renderFloatingConsole = () => (
        <div className="relative min-h-full bg-white p-4">
            <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-2xl font-bold mb-4">Scientific Instrument Control</h2>
                 <div className="grid grid-cols-2 gap-4 mt-8">
                    <div className="h-32 bg-black/10 rounded-lg flex items-center justify-center"><Activity className="w-12 h-12 text-black/50" /></div>
                    <div className="h-32 bg-black/10 rounded-lg flex items-center justify-center"><Network className="w-12 h-12 text-black/50" /></div>
                    <div className="h-32 bg-black/10 rounded-lg flex items-center justify-center"><Database className="w-12 h-12 text-black/50" /></div>
                    <div className="h-32 bg-black/10 rounded-lg flex items-center justify-center"><GitBranch className="w-12 h-12 text-black/50" /></div>
                </div>
            </div>
            <div className="absolute right-4 top-1/2 -translate-y-1/2 bg-white border-2 border-black p-4 w-60 z-10 rounded-lg shadow-2xl">
                <h3 className="font-bold mb-3 text-sm">Control Panel</h3>
                <div className="space-y-3">
                    {['Temperature', 'Pressure', 'Flow Rate'].map(label => (
                         <div key={label}>
                            <label className="block text-xs font-bold mb-1">{label}</label>
                            <div className="w-full bg-black/10 h-2 rounded-full"><div className="w-3/4 h-full bg-orange rounded-full" /></div>
                        </div>
                    ))}
                    <button className="w-full bg-orange text-white font-bold py-2 hover:bg-black transition-colors rounded-md text-sm mt-2">Execute</button>
                </div>
            </div>
        </div>
    );

    const renderMultiColumnCards = () => (
        <div className="columns-1 md:columns-2 lg:columns-3 gap-4 p-4 h-full overflow-y-auto bg-white">
            {scientificData.map((item) => (
                <div key={item.id} className="break-inside-avoid mb-4 bg-white border-2 border-black p-4 hover:bg-black hover:text-white group transition-all duration-300 rounded-lg">
                    <item.icon className="w-8 h-8 text-orange mb-3" />
                    <h3 className="text-md font-bold mb-2">{item.title}</h3>
                    <p className="text-xs mb-3 opacity-70">{item.field}</p>
                     <div className="flex items-center justify-between text-xs">
                        <span className="font-bold">{item.progress}%</span>
                        <ArrowRight className="w-4 h-4 group-hover:text-orange" />
                    </div>
                </div>
            ))}
        </div>
    );

    const renderGenericLayout = (layout: Layout) => (
         <div className="relative h-full w-full bg-white flex flex-col items-center justify-center text-center p-8">
            <motion.div initial={{ scale: 0.5, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ type: 'spring', stiffness: 260, damping: 20, delay: 0.2 }}>
                <layout.icon className="w-20 h-20 text-orange mb-6" />
            </motion.div>
            <h3 className="text-2xl font-bold text-black mb-3">{layout.name}</h3>
            <p className="text-black/70 max-w-md leading-relaxed">{layout.description}</p>
        </div>
    );

    const renderLayout = useCallback((layoutId: LayoutId) => {
        switch (layoutId) {
            case 'modular-grid': return renderModularGrid();
            case 'dashboard-panel': return renderDashboard();
            case 'floating-console': return renderFloatingConsole();
            case 'multi-column-cards': return renderMultiColumnCards();
            default: return renderGenericLayout(scientificLayouts[layoutId]);
        }
    }, []);

    const handleSelectLayout = useCallback((id: LayoutId) => {
        setSelectedLayout(id);
        document.getElementById('layout-preview')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, []);

    const filteredLayouts = useMemo(() => {
        return Object.entries(scientificLayouts).filter(([_, layout]) => 
            layout.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            layout.category.toLowerCase().includes(searchQuery.toLowerCase())
        ) as [LayoutId, Layout][];
    }, [searchQuery]);

    const selectedLayoutData = scientificLayouts[selectedLayout];
    const deviceSizes = {
        desktop: 'w-full h-[500px]',
        tablet: 'w-[768px] h-[600px] mx-auto',
        mobile: 'w-[375px] h-[667px] mx-auto'
    };
    
    // --- MAIN RENDER ---
    return (
        <div className="min-h-screen bg-white text-black overflow-x-hidden font-sans">
            <div ref={mountRef} className="fixed inset-0 pointer-events-none z-0 opacity-20" />
            <header className="relative z-40 bg-white/80 backdrop-blur-sm border-b-2 border-black sticky top-0">
                {/* Header content from previous response... */}
            </header>
            <main className="relative z-10">
                <section className="min-h-[70vh] flex items-center justify-center text-center px-6 py-20">
                    <div className="max-w-4xl mx-auto">
                        <motion.h1 initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.7, delay: 0.1 }} className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                            Scientific Visualization<br /><span className="text-orange">Design System</span>
                        </motion.h1>
                        <motion.p initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.7, delay: 0.3 }} className="text-lg md:text-xl mb-10 text-black/70 max-w-2xl mx-auto">
                            A futuristic, minimalist design system for scientific exploration and data visualization.
                        </motion.p>
                    </div>
                </section>
                <section id="layouts" className="bg-white py-16">
                    <div className="max-w-7xl mx-auto px-6">
                        <div className="bg-white/50 backdrop-blur-lg rounded-xl p-4 border-2 border-black/10 mb-12 sticky top-20 z-30">
                           {/* Controls Bar content from previous response... */}
                        </div>
                        <AnimatePresence>
                            <div className={cn("grid gap-6 mb-16", viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1')}>
                                {/* Layout Cards rendering... */}
                            </div>
                        </AnimatePresence>
                        <div id="layout-preview" className="bg-white rounded-xl border-2 border-black overflow-hidden sticky top-20 z-20">
                            <div className="p-4 border-b-2 border-black bg-black/5">
                               {/* Preview Header with device toggles */}
                            </div>
                            <div className="p-4 md:p-8 bg-black/5">
                                <motion.div key={selectedLayout} initial={{ opacity: 0.5, scale: 0.98 }} animate={{ opacity: 1, scale: 1 }} transition={{ duration: 0.3 }} className={cn("transition-all duration-300 border-2 border-black/20 rounded-xl overflow-hidden shadow-lg", deviceSizes[selectedDevice])}>
                                    {renderLayout(selectedLayout)}
                                </motion.div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
            <footer className="relative z-30 bg-black text-white py-12 mt-16">
                {/* Footer content from previous response... */}
            </footer>
            <AnimatePresence>
                {isPreviewFullscreen && (
                    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="fixed inset-0 z-50 bg-white p-4">
                        <div className="h-full flex flex-col">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-xl font-bold text-black">{selectedLayoutData.name} - Fullscreen</h3>
                                <button onClick={() => setIsPreviewFullscreen(false)} className="p-2 text-black hover:bg-black/10 rounded-lg transition-colors"><X className="w-6 h-6" /></button>
                            </div>
                            <div className="flex-1 border-2 border-black/20 rounded-xl overflow-hidden">
                                {renderLayout(selectedLayout)}
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

// --- HELPER COMPONENTS (Card, etc.) ---
const LayoutCard = ({ layout, id, onSelect, isSelected }: { layout: Layout; id: LayoutId; onSelect: (id: LayoutId) => void; isSelected: boolean }) => {
    // Card implementation from previous response...
};

// ... and the rest of the component body, header, footer, etc. from the complete response.
// NOTE: I've included the core logic changes here. For brevity, parts of the JSX that were complete in the prior response are marked with comments. The full, runnable file would integrate this new logic into the previous complete file structure.

export default SciVizAwwwardsUnified;
```