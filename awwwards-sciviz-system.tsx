import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';
import {
  Menu,
  X,
  ArrowRight,
  ArrowDown,
  Grid,
  Layers,
  Eye,
  Code,
  Download,
  Play,
  Pause,
  RotateCcw,
  Maximize2,
  ChevronRight,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Star,
  Zap,
  Database,
  BarChart3,
  Activity,
  Atom,
  Dna,
  Microscope,
  Telescope,
  Brain,
  Cpu,
  Network,
  GitBranch
} from 'lucide-react';

const SciVizDesignSystem = () => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const meshRef = useRef(null);
  const animationRef = useRef(null);
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeLayout, setActiveLayout] = useState('modular-grid');
  const [isAnimationPaused, setIsAnimationPaused] = useState(false);
  const [currentDemo, setCurrentDemo] = useState(0);
  const [scrollY, setScrollY] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Layout definitions
  const layouts = {
    'modular-grid': {
      name: 'Modular Grid',
      category: 'Grid Systems',
      description: 'Scientific data organized in flexible, responsive grid modules',
      icon: Grid,
      pattern: 'grid-modular'
    },
    'infinite-scroll': {
      name: 'Infinite Scroll Lab',
      category: 'Data Exploration',
      description: 'Endless discovery through continuous data visualization',
      icon: ArrowDown,
      pattern: 'scroll-infinite'
    },
    'data-canvas': {
      name: 'Full-Width Canvas',
      category: 'Immersive Views',
      description: 'Edge-to-edge scientific visualization playground',
      icon: Maximize2,
      pattern: 'canvas-full'
    },
    'floating-sidebar': {
      name: 'Floating Console',
      category: 'Control Interfaces',
      description: 'Hovering control panel for complex scientific instruments',
      icon: Layers,
      pattern: 'sidebar-floating'
    },
    'dashboard-panel': {
      name: 'Scientific Dashboard',
      category: 'Analytics',
      description: 'Real-time monitoring of experimental parameters',
      icon: BarChart3,
      pattern: 'dashboard-scientific'
    },
    'card-gallery': {
      name: 'Multi-Column Cards',
      category: 'Content Display',
      description: 'Research findings presented in elegant card collections',
      icon: Square,
      pattern: 'cards-multi'
    }
  };

  // Sample scientific data
  const scientificData = [
    { id: 1, title: 'Quantum Entanglement', field: 'Physics', icon: Atom, progress: 87, status: 'active' },
    { id: 2, title: 'Neural Networks', field: 'AI/ML', icon: Brain, progress: 94, status: 'complete' },
    { id: 3, title: 'Genetic Sequencing', field: 'Biology', icon: Dna, progress: 76, status: 'active' },
    { id: 4, title: 'Stellar Formation', field: 'Astronomy', icon: Telescope, progress: 62, status: 'pending' },
    { id: 5, title: 'Protein Folding', field: 'Biochemistry', icon: Microscope, progress: 89, status: 'active' },
    { id: 6, title: 'Quantum Computing', field: 'Computing', icon: Cpu, progress: 73, status: 'active' }
  ];

  // Three.js setup
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0xffffff, 0);
    mountRef.current.appendChild(renderer.domElement);
    
    // Create scientific visualization geometry
    const geometry = new THREE.IcosahedronGeometry(2, 2);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      wireframe: true,
      transparent: true,
      opacity: 0.8
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);
    
    // Add additional geometric elements
    const smallGeometry = new THREE.OctahedronGeometry(0.5);
    const smallMaterial = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      transparent: true,
      opacity: 0.6
    });
    
    for (let i = 0; i < 8; i++) {
      const smallMesh = new THREE.Mesh(smallGeometry, smallMaterial);
      const angle = (i / 8) * Math.PI * 2;
      smallMesh.position.x = Math.cos(angle) * 4;
      smallMesh.position.y = Math.sin(angle) * 4;
      smallMesh.position.z = Math.sin(angle * 2) * 2;
      scene.add(smallMesh);
    }
    
    camera.position.z = 8;
    
    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;
    cameraRef.current = camera;
    meshRef.current = mesh;
    
    // Animation loop
    const animate = () => {
      animationRef.current = requestAnimationFrame(animate);
      
      if (!isAnimationPaused && meshRef.current) {
        meshRef.current.rotation.x += 0.005;
        meshRef.current.rotation.y += 0.01;
        
        // React to mouse position
        meshRef.current.position.x = (mousePosition.x - 0.5) * 0.5;
        meshRef.current.position.y = (mousePosition.y - 0.5) * -0.5;
        
        // React to scroll
        meshRef.current.scale.set(
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001
        );
        
        // Animate small meshes
        scene.children.forEach((child, index) => {
          if (child !== meshRef.current) {
            child.rotation.x += 0.02;
            child.rotation.y += 0.01;
            const time = Date.now() * 0.001;
            child.position.y += Math.sin(time + index) * 0.01;
          }
        });
      }
      
      renderer.render(scene, camera);
    };
    
    animate();
    
    // Handle resize
    const handleResize = () => {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [isAnimationPaused, mousePosition, scrollY]);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight
      });
    };
    
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Layout renderers
  const renderModularGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-8">
      {scientificData.map((item, index) => (
        <div 
          key={item.id}
          className="bg-white border-2 border-black p-6 group hover:bg-black hover:text-white transition-all duration-300 cursor-pointer"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="flex items-center justify-between mb-4">
            <item.icon className="w-8 h-8 text-black group-hover:text-[#FF6600] transition-colors" />
            <div className={`w-3 h-3 rounded-full ${
              item.status === 'complete' ? 'bg-[#FF6600]' : 
              item.status === 'active' ? 'bg-black group-hover:bg-white' : 
              'bg-black/30 group-hover:bg-white/30'
            }`} />
          </div>
          <h3 className="text-xl font-bold mb-2">{item.title}</h3>
          <p className="text-sm mb-4 opacity-70">{item.field}</p>
          <div className="w-full bg-black/10 group-hover:bg-white/10 h-2">
            <div 
              className="h-full bg-[#FF6600] transition-all duration-1000"
              style={{ width: `${item.progress}%` }}
            />
          </div>
          <div className="mt-2 text-sm opacity-70">{item.progress}% Complete</div>
        </div>
      ))}
    </div>
  );

  const renderInfiniteScroll = () => (
    <div className="space-y-4 p-8">
      {[...Array(20)].map((_, index) => (
        <div 
          key={index}
          className="flex items-center space-x-6 p-4 border-b border-black/10 hover:bg-[#FF6600]/5 transition-all duration-300"
        >
          <div className="w-12 h-12 bg-black flex items-center justify-center text-white font-bold">
            {String(index + 1).padStart(2, '0')}
          </div>
          <div className="flex-1">
            <h4 className="font-bold">Research Entry {index + 1}</h4>
            <p className="text-sm opacity-70">Advanced scientific discovery and analysis</p>
          </div>
          <div className="w-8 h-8 bg-[#FF6600] rounded-full flex items-center justify-center">
            <ArrowRight className="w-4 h-4 text-white" />
          </div>
        </div>
      ))}
    </div>
  );

  const renderDataCanvas = () => (
    <div className="relative h-[600px] bg-white border-4 border-black overflow-hidden">
      <div className="absolute inset-0 p-8">
        <div className="grid grid-cols-8 grid-rows-6 h-full gap-2">
          {[...Array(48)].map((_, index) => (
            <div 
              key={index}
              className={`${
                Math.random() > 0.7 ? 'bg-[#FF6600]' : 
                Math.random() > 0.5 ? 'bg-black' : 'bg-black/10'
              } transition-all duration-1000 hover:bg-[#FF6600] cursor-pointer`}
              style={{ 
                animationDelay: `${index * 20}ms`,
                transform: `scale(${0.8 + Math.random() * 0.4})`
              }}
            />
          ))}
        </div>
      </div>
      <div className="absolute top-4 left-4 bg-white p-4 border-2 border-black">
        <h3 className="font-bold mb-2">Data Visualization</h3>
        <p className="text-sm">Interactive scientific data matrix</p>
      </div>
    </div>
  );

  const renderFloatingSidebar = () => (
    <div className="relative min-h-[600px] bg-white p-8">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-4xl font-bold mb-8">Scientific Instrument Control</h2>
        <div className="grid grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Activity className="w-16 h-16 text-black/50" />
            </div>
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Network className="w-16 h-16 text-black/50" />
            </div>
          </div>
          <div className="space-y-4">
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Database className="w-16 h-16 text-black/50" />
            </div>
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <GitBranch className="w-16 h-16 text-black/50" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating Control Panel */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 bg-white border-4 border-black p-6 w-80 z-10">
        <h3 className="font-bold mb-4">Control Panel</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-bold mb-2">Temperature</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-3/4 h-full bg-[#FF6600]" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-bold mb-2">Pressure</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-1/2 h-full bg-[#FF6600]" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-bold mb-2">Flow Rate</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-5/6 h-full bg-[#FF6600]" />
            </div>
          </div>
          <button className="w-full bg-[#FF6600] text-white font-bold py-3 hover:bg-black transition-colors">
            Execute Protocol
          </button>
        </div>
      </div>
    </div>
  );

  const renderDashboard = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-8">
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white border-2 border-black p-6">
          <h3 className="font-bold mb-4">Experimental Progress</h3>
          <div className="h-40 bg-black/5 flex items-end justify-between p-4">
            {[40, 60, 35, 80, 55, 90, 75, 85].map((height, index) => (
              <div 
                key={index}
                className="bg-[#FF6600] w-8 transition-all duration-1000 hover:bg-black"
                style={{ height: `${height}%`, animationDelay: `${index * 100}ms` }}
              />
            ))}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-6">
          <div className="bg-white border-2 border-black p-6">
            <h4 className="font-bold mb-2">Active Experiments</h4>
            <div className="text-3xl font-bold text-[#FF6600]">24</div>
          </div>
          <div className="bg-white border-2 border-black p-6">
            <h4 className="font-bold mb-2">Success Rate</h4>
            <div className="text-3xl font-bold text-[#FF6600]">94.2%</div>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        <div className="bg-black text-white p-6">
          <h3 className="font-bold mb-4">System Status</h3>
          <div className="space-y-3">
            {['Quantum Processor', 'Neural Interface', 'Data Pipeline', 'Security Layer'].map((system, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm">{system}</span>
                <div className="w-3 h-3 bg-[#FF6600] rounded-full" />
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white border-2 border-black p-6">
          <h4 className="font-bold mb-4">Recent Results</h4>
          <div className="space-y-2">
            {scientificData.slice(0, 3).map((item) => (
              <div key={item.id} className="flex items-center space-x-3 text-sm">
                <item.icon className="w-4 h-4 text-[#FF6600]" />
                <span>{item.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderCardGallery = () => (
    <div className="columns-1 md:columns-2 lg:columns-3 gap-6 p-8">
      {scientificData.map((item, index) => (
        <div 
          key={item.id}
          className="break-inside-avoid mb-6 bg-white border-2 border-black p-6 hover:bg-black hover:text-white group transition-all duration-300"
          style={{ animationDelay: `${index * 150}ms` }}
        >
          <item.icon className="w-12 h-12 text-[#FF6600] mb-4" />
          <h3 className="text-xl font-bold mb-3">{item.title}</h3>
          <p className="text-sm mb-4 opacity-70">{item.field}</p>
          <div className="space-y-2 mb-4">
            <div className="text-xs font-bold">PROGRESS ANALYSIS</div>
            <div className="w-full bg-black/10 group-hover:bg-white/10 h-1">
              <div 
                className="h-full bg-[#FF6600] transition-all duration-1000"
                style={{ width: `${item.progress}%` }}
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold">{item.progress}%</span>
            <ArrowRight className="w-4 h-4 group-hover:text-[#FF6600]" />
          </div>
        </div>
      ))}
    </div>
  );

  const renderLayout = () => {
    switch (activeLayout) {
      case 'modular-grid':
        return renderModularGrid();
      case 'infinite-scroll':
        return renderInfiniteScroll();
      case 'data-canvas':
        return renderDataCanvas();
      case 'floating-sidebar':
        return renderFloatingSidebar();
      case 'dashboard-panel':
        return renderDashboard();
      case 'card-gallery':
        return renderCardGallery();
      default:
        return renderModularGrid();
    }
  };

  return (
    <div className="min-h-screen bg-white text-black overflow-x-hidden">
      {/* Three.js Background */}
      <div 
        ref={mountRef} 
        className="fixed inset-0 pointer-events-none z-0"
        style={{ mixBlendMode: 'multiply' }}
      />
      
      {/* Header */}
      <header className="relative z-40 bg-white border-b-4 border-black">
        <nav className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-[#FF6600] flex items-center justify-center font-bold text-white text-xl">
                S
              </div>
              <div>
                <h1 className="text-2xl font-bold">SciViz</h1>
                <p className="text-sm opacity-70">Design System</p>
              </div>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <a href="#layouts" className="font-bold hover:text-[#FF6600] transition-colors">
                Layouts
              </a>
              <a href="#documentation" className="font-bold hover:text-[#FF6600] transition-colors">
                Documentation
              </a>
              <a href="#download" className="font-bold hover:text-[#FF6600] transition-colors">
                Download
              </a>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsAnimationPaused(!isAnimationPaused)}
                className="p-2 border-2 border-black hover:bg-black hover:text-white transition-all"
                title={isAnimationPaused ? 'Resume Animation' : 'Pause Animation'}
              >
                {isAnimationPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
              </button>
              
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 border-2 border-black hover:bg-black hover:text-white transition-all"
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>
          </div>
          
          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden mt-4 pt-4 border-t-2 border-black">
              <div className="space-y-2">
                <a href="#layouts" className="block font-bold py-2 hover:text-[#FF6600] transition-colors">
                  Layouts
                </a>
                <a href="#documentation" className="block font-bold py-2 hover:text-[#FF6600] transition-colors">
                  Documentation
                </a>
                <a href="#download" className="block font-bold py-2 hover:text-[#FF6600] transition-colors">
                  Download
                </a>
              </div>
            </div>
          )}
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-30 min-h-screen flex items-center justify-center text-center px-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
            Scientific
            <br />
            <span className="text-[#FF6600]">Visualization</span>
            <br />
            Design System
          </h1>
          <p className="text-xl md:text-2xl mb-12 opacity-70 max-w-2xl mx-auto">
            A futuristic, minimalist design system for scientific exploration 
            and data visualization. Every pixel contributes to discovery.
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center">
            <button className="bg-[#FF6600] text-white px-8 py-4 font-bold text-lg hover:bg-black transition-all transform hover:scale-105">
              Explore Layouts
            </button>
            <button className="border-2 border-black px-8 py-4 font-bold text-lg hover:bg-black hover:text-white transition-all transform hover:scale-105">
              View Documentation
            </button>
          </div>
        </div>
      </section>

      {/* Layout Selector */}
      <section id="layouts" className="relative z-30 bg-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-6xl font-bold mb-4">
              Layout <span className="text-[#FF6600]">Systems</span>
            </h2>
            <p className="text-xl opacity-70 max-w-2xl mx-auto">
              Choose from multiple innovative layout patterns, each designed 
              for different scientific visualization needs.
            </p>
          </div>
          
          {/* Layout Navigation */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
            {Object.entries(layouts).map(([key, layout]) => (
              <button
                key={key}
                onClick={() => setActiveLayout(key)}
                className={`p-6 border-2 border-black text-left transition-all transform hover:scale-105 ${
                  activeLayout === key 
                    ? 'bg-black text-white' 
                    : 'bg-white hover:bg-[#FF6600] hover:text-white hover:border-[#FF6600]'
                }`}
              >
                <layout.icon className={`w-8 h-8 mb-3 ${
                  activeLayout === key ? 'text-[#FF6600]' : ''
                }`} />
                <h3 className="font-bold text-sm mb-1">{layout.name}</h3>
                <p className="text-xs opacity-70">{layout.category}</p>
              </button>
            ))}
          </div>
          
          {/* Active Layout Info */}
          <div className="bg-black text-white p-8 mb-8">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-3xl font-bold mb-2">
                  {layouts[activeLayout].name}
                </h3>
                <p className="text-[#FF6600] font-bold mb-4">
                  {layouts[activeLayout].category}
                </p>
                <p className="text-lg opacity-80 max-w-2xl">
                  {layouts[activeLayout].description}
                </p>
              </div>
              <div className="flex space-x-2">
                <button className="p-3 bg-[#FF6600] text-white hover:bg-white hover:text-black transition-all">
                  <Code className="w-5 h-5" />
                </button>
                <button className="p-3 bg-[#FF6600] text-white hover:bg-white hover:text-black transition-all">
                  <Download className="w-5 h-5" />
                </button>
                <button className="p-3 bg-[#FF6600] text-white hover:bg-white hover:text-black transition-all">
                  <Eye className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Layout Demonstration */}
          <div className="border-4 border-black bg-white">
            {renderLayout()}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-30 bg-black text-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-6xl font-bold mb-4">
              System <span className="text-[#FF6600]">Features</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: Zap, title: 'Lightning Fast', desc: 'Optimized for scientific computing performance' },
              { icon: Atom, title: 'Scientific Accuracy', desc: 'Precise visualizations with mathematical integrity' },
              { icon: Network, title: 'Modular System', desc: 'Flexible components for any research need' },
              { icon: Activity, title: 'Real-time Data', desc: 'Live updates and interactive exploration' }
            ].map((feature, index) => (
              <div key={index} className="text-center p-6 border border-white/20 hover:border-[#FF6600] transition-all">
                <feature.icon className="w-12 h-12 text-[#FF6600] mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                <p className="opacity-70">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Download Section */}
      <section id="download" className="relative z-30 bg-white py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-6xl font-bold mb-8">
            Start <span className="text-[#FF6600]">Building</span>
          </h2>
          <p className="text-xl opacity-70 mb-12 max-w-2xl mx-auto">
            Download the complete SciViz Design System and start creating 
            beautiful scientific interfaces today.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="border-2 border-black p-8 hover:bg-black hover:text-white transition-all">
              <h3 className="text-2xl font-bold mb-4">Components</h3>
              <p className="text-sm opacity-70 mb-6">Complete UI component library</p>
              <button className="w-full bg-[#FF6600] text-white py-3 font-bold hover:bg-black transition-all">
                Download
              </button>
            </div>
            
            <div className="border-2 border-black p-8 hover:bg-black hover:text-white transition-all">
              <h3 className="text-2xl font-bold mb-4">Templates</h3>
              <p className="text-sm opacity-70 mb-6">Ready-to-use layout templates</p>
              <button className="w-full bg-[#FF6600] text-white py-3 font-bold hover:bg-black transition-all">
                Download
              </button>
            </div>
            
            <div className="border-2 border-black p-8 hover:bg-black hover:text-white transition-all">
              <h3 className="text-2xl font-bold mb-4">Documentation</h3>
              <p className="text-sm opacity-70 mb-6">Complete implementation guide</p>
              <button className="w-full bg-[#FF6600] text-white py-3 font-bold hover:bg-black transition-all">
                Download
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-30 bg-black text-white py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-[#FF6600] flex items-center justify-center font-bold text-sm">
                S
              </div>
              <span className="font-bold">SciViz Design System</span>
            </div>
            
            <div className="text-sm opacity-70">
              © 2025 SciViz. Designed for scientific excellence.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SciVizDesignSystem;