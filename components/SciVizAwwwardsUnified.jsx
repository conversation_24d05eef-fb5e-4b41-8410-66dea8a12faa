import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';
import {
  Menu,
  X,
  ArrowRight,
  ArrowDown,
  Grid,
  Layers,
  Eye,
  Code,
  Download,
  Play,
  Pause,
  RotateCcw,
  Maximize2,
  ChevronRight,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Star,
  Zap,
  Database,
  BarChart3,
  Activity,
  Atom,
  Dna,
  Microscope,
  Telescope,
  Brain,
  Cpu,
  Network,
  GitBranch,
  Search,
  Settings,
  Monitor,
  Tablet,
  Smartphone,
  Grid3X3,
  List,
  LayoutGrid,
  Infinity,
  Waves,
  Layers3
} from 'lucide-react';

const SciVizAwwwardsUnified = () => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const meshRef = useRef(null);
  const animationRef = useRef(null);
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeLayout, setActiveLayout] = useState('modular-grid');
  const [isAnimationPaused, setIsAnimationPaused] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [selectedDevice, setSelectedDevice] = useState('desktop');
  const [isPreviewFullscreen, setIsPreviewFullscreen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState('grid');

  // Layout definitions combining both systems
  const layouts = {
    'modular-grid': {
      name: 'Modular Grid',
      category: 'Grid Systems',
      description: 'Scientific data organized in flexible, responsive grid modules',
      icon: Grid,
      complexity: 'Medium',
      performance: { speed: 94, accessibility: 97, mobile: 99 },
      lastUpdated: '2025-01-09'
    },
    'infinite-scroll': {
      name: 'Infinite Scroll Lab',
      category: 'Data Exploration',
      description: 'Endless discovery through continuous data visualization',
      icon: ArrowDown,
      complexity: 'Medium',
      performance: { speed: 88, accessibility: 93, mobile: 95 },
      lastUpdated: '2025-01-07'
    },
    'full-width-canvas': {
      name: 'Full-Width Canvas',
      category: 'Immersive Views',
      description: 'Edge-to-edge scientific visualization playground',
      icon: Maximize2,
      complexity: 'Advanced',
      performance: { speed: 85, accessibility: 95, mobile: 97 },
      lastUpdated: '2025-01-05'
    },
    'floating-console': {
      name: 'Floating Console',
      category: 'Control Interfaces',
      description: 'Hovering control panel for complex scientific instruments',
      icon: Layers,
      complexity: 'Advanced',
      performance: { speed: 86, accessibility: 89, mobile: 91 },
      lastUpdated: '2024-12-27'
    },
    'scientific-dashboard': {
      name: 'Scientific Dashboard',
      category: 'Analytics',
      description: 'Real-time monitoring of experimental parameters',
      icon: BarChart3,
      complexity: 'Advanced',
      performance: { speed: 92, accessibility: 96, mobile: 98 },
      lastUpdated: '2025-01-02'
    },
    'multi-column-cards': {
      name: 'Multi-Column Cards',
      category: 'Content Display',
      description: 'Research findings presented in elegant card collections',
      icon: Square,
      complexity: 'Medium',
      performance: { speed: 93, accessibility: 96, mobile: 98 },
      lastUpdated: '2024-12-31'
    }
  };

  // Sample scientific data
  const scientificData = [
    { id: 1, title: 'Quantum Entanglement', field: 'Physics', icon: Atom, progress: 87, status: 'active' },
    { id: 2, title: 'Neural Networks', field: 'AI/ML', icon: Brain, progress: 94, status: 'complete' },
    { id: 3, title: 'Genetic Sequencing', field: 'Biology', icon: Dna, progress: 76, status: 'active' },
    { id: 4, title: 'Stellar Formation', field: 'Astronomy', icon: Telescope, progress: 62, status: 'pending' },
    { id: 5, title: 'Protein Folding', field: 'Biochemistry', icon: Microscope, progress: 89, status: 'active' },
    { id: 6, title: 'Quantum Computing', field: 'Computing', icon: Cpu, progress: 73, status: 'active' }
  ];

  const categories = ['all', 'Grid Systems', 'Data Exploration', 'Immersive Views', 'Control Interfaces', 'Analytics', 'Content Display'];

  // Three.js setup
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0xffffff, 0);
    mountRef.current.appendChild(renderer.domElement);
    
    // Create scientific visualization geometry
    const geometry = new THREE.IcosahedronGeometry(2, 2);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);
    
    // Add additional geometric elements
    const smallGeometry = new THREE.OctahedronGeometry(0.5);
    const smallMaterial = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      transparent: true,
      opacity: 0.2
    });
    
    for (let i = 0; i < 8; i++) {
      const smallMesh = new THREE.Mesh(smallGeometry, smallMaterial);
      const angle = (i / 8) * Math.PI * 2;
      smallMesh.position.x = Math.cos(angle) * 4;
      smallMesh.position.y = Math.sin(angle) * 4;
      smallMesh.position.z = Math.sin(angle * 2) * 2;
      scene.add(smallMesh);
    }
    
    camera.position.z = 8;
    
    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;
    cameraRef.current = camera;
    meshRef.current = mesh;
    
    // Animation loop
    const animate = () => {
      animationRef.current = requestAnimationFrame(animate);
      
      if (!isAnimationPaused && meshRef.current) {
        meshRef.current.rotation.x += 0.005;
        meshRef.current.rotation.y += 0.01;
        
        // React to mouse position
        meshRef.current.position.x = (mousePosition.x - 0.5) * 0.5;
        meshRef.current.position.y = (mousePosition.y - 0.5) * -0.5;
        
        // React to scroll
        meshRef.current.scale.set(
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001
        );
        
        // Animate small meshes
        scene.children.forEach((child, index) => {
          if (child !== meshRef.current) {
            child.rotation.x += 0.02;
            child.rotation.y += 0.01;
            const time = Date.now() * 0.001;
            child.position.y += Math.sin(time + index) * 0.01;
          }
        });
      }
      
      renderer.render(scene, camera);
    };
    
    animate();
    
    // Handle resize
    const handleResize = () => {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [isAnimationPaused, mousePosition, scrollY]);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight
      });
    };
    
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Layout renderers - simplified for initial setup
  const renderModularGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-8">
      {scientificData.map((item, index) => (
        <div 
          key={item.id}
          className="bg-white border-2 border-black p-6 group hover:bg-black hover:text-white transition-all duration-300 cursor-pointer"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="flex items-center justify-between mb-4">
            <item.icon className="w-8 h-8 text-black group-hover:text-orange transition-colors" />
            <div className={`w-3 h-3 rounded-full ${
              item.status === 'complete' ? 'bg-orange' : 
              item.status === 'active' ? 'bg-black group-hover:bg-white' : 
              'bg-black/30 group-hover:bg-white/30'
            }`} />
          </div>
          <h3 className="text-xl font-bold mb-2">{item.title}</h3>
          <p className="text-sm mb-4 opacity-70">{item.field}</p>
          <div className="w-full bg-black/10 group-hover:bg-white/10 h-2">
            <div 
              className="h-full bg-orange transition-all duration-1000"
              style={{ width: `${item.progress}%` }}
            />
          </div>
          <div className="mt-2 text-sm opacity-70">{item.progress}% Complete</div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-white text-black relative overflow-hidden">
      {/* 3D Background */}
      <div 
        ref={mountRef} 
        className="fixed inset-0 pointer-events-none z-0"
        style={{ opacity: 0.3 }}
      />
      
      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b-4 border-black bg-white">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-black flex items-center justify-center">
                  <Grid className="w-6 h-6 text-orange" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">SciViz</h1>
                  <p className="text-sm opacity-70">Design System</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* 3D Animation Controls */}
                <div className="flex items-center space-x-2 border-2 border-black p-2">
                  <button
                    onClick={() => setIsAnimationPaused(!isAnimationPaused)}
                    className="p-2 hover:bg-black hover:text-white transition-colors"
                  >
                    {isAnimationPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  </button>
                  <button className="p-2 hover:bg-black hover:text-white transition-colors">
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>
                
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="p-2 border-2 border-black hover:bg-black hover:text-white transition-colors"
                >
                  {isMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-6">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-6xl md:text-8xl font-bold mb-6">
              Scientific
              <br />
              Visualization
              <br />
              Design System
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-70">
              A futuristic, minimalist design system for scientific exploration and data
              visualization. Every pixel contributes to discovery.
            </p>
            <button className="bg-black text-white px-8 py-4 font-bold hover:bg-orange transition-colors">
              View Documentation
            </button>
          </div>
        </section>

        {/* Layout Preview */}
        <section className="py-20 px-6 border-t-4 border-black">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4">Layout Systems</h2>
              <p className="text-xl opacity-70 max-w-3xl mx-auto">
                Choose from multiple innovative layout patterns, each designed for
                different scientific visualization needs.
              </p>
            </div>

            {/* Preview Section */}
            <div className="border-4 border-black bg-white">
              <div className="border-b-2 border-black p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-xl font-bold">{layouts[activeLayout]?.name}</h3>
                  <span className="text-sm opacity-70">{layouts[activeLayout]?.category}</span>
                </div>
              </div>
              
              <div className="min-h-[600px]">
                {renderModularGrid()}
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default SciVizAwwwardsUnified;
