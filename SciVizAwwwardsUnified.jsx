import React, { useState, useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';
import {
  Menu,
  X,
  ArrowRight,
  ArrowDown,
  Grid,
  Layers,
  Eye,
  Code,
  Download,
  Play,
  Pause,
  RotateCcw,
  Maximize2,
  ChevronRight,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Star,
  Zap,
  Database,
  BarChart3,
  Activity,
  Atom,
  Dna,
  Microscope,
  Telescope,
  Brain,
  Cpu,
  Network,
  GitBranch,
  Search,
  Settings,
  Monitor,
  Tablet,
  Smartphone,
  Grid3X3,
  List,
  LayoutGrid,
  Infinity,
  Waves,
  Layers3
} from 'lucide-react';

const SciVizAwwwardsUnified = () => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const meshRef = useRef(null);
  const animationRef = useRef(null);
  
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeLayout, setActiveLayout] = useState('modular-grid');
  const [isAnimationPaused, setIsAnimationPaused] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [selectedDevice, setSelectedDevice] = useState('desktop');
  const [isPreviewFullscreen, setIsPreviewFullscreen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState('grid');

  // Layout definitions combining both systems
  const layouts = {
    'modular-grid': {
      name: 'Modular Grid',
      category: 'Grid Systems',
      description: 'Scientific data organized in flexible, responsive grid modules',
      icon: Grid,
      complexity: 'Medium',
      performance: { speed: 94, accessibility: 97, mobile: 99 },
      lastUpdated: '2025-01-09'
    },
    'infinite-scroll': {
      name: 'Infinite Scroll Lab',
      category: 'Data Exploration',
      description: 'Endless discovery through continuous data visualization',
      icon: ArrowDown,
      complexity: 'Medium',
      performance: { speed: 88, accessibility: 93, mobile: 95 },
      lastUpdated: '2025-01-07'
    },
    'full-width-canvas': {
      name: 'Full-Width Canvas',
      category: 'Immersive Views',
      description: 'Edge-to-edge scientific visualization playground',
      icon: Maximize2,
      complexity: 'Advanced',
      performance: { speed: 85, accessibility: 95, mobile: 97 },
      lastUpdated: '2025-01-05'
    },
    'floating-console': {
      name: 'Floating Console',
      category: 'Control Interfaces',
      description: 'Hovering control panel for complex scientific instruments',
      icon: Layers,
      complexity: 'Advanced',
      performance: { speed: 86, accessibility: 89, mobile: 91 },
      lastUpdated: '2024-12-27'
    },
    'scientific-dashboard': {
      name: 'Scientific Dashboard',
      category: 'Analytics',
      description: 'Real-time monitoring of experimental parameters',
      icon: BarChart3,
      complexity: 'Advanced',
      performance: { speed: 92, accessibility: 96, mobile: 98 },
      lastUpdated: '2025-01-02'
    },
    'multi-column-cards': {
      name: 'Multi-Column Cards',
      category: 'Content Display',
      description: 'Research findings presented in elegant card collections',
      icon: Square,
      complexity: 'Medium',
      performance: { speed: 93, accessibility: 96, mobile: 98 },
      lastUpdated: '2024-12-31'
    }
  };

  // Sample scientific data
  const scientificData = [
    { id: 1, title: 'Quantum Entanglement', field: 'Physics', icon: Atom, progress: 87, status: 'active' },
    { id: 2, title: 'Neural Networks', field: 'AI/ML', icon: Brain, progress: 94, status: 'complete' },
    { id: 3, title: 'Genetic Sequencing', field: 'Biology', icon: Dna, progress: 76, status: 'active' },
    { id: 4, title: 'Stellar Formation', field: 'Astronomy', icon: Telescope, progress: 62, status: 'pending' },
    { id: 5, title: 'Protein Folding', field: 'Biochemistry', icon: Microscope, progress: 89, status: 'active' },
    { id: 6, title: 'Quantum Computing', field: 'Computing', icon: Cpu, progress: 73, status: 'active' }
  ];

  const categories = ['all', 'Grid Systems', 'Data Exploration', 'Immersive Views', 'Control Interfaces', 'Analytics', 'Content Display'];

  // Three.js setup
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0xffffff, 0);
    mountRef.current.appendChild(renderer.domElement);
    
    // Create scientific visualization geometry
    const geometry = new THREE.IcosahedronGeometry(2, 2);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);
    
    // Add additional geometric elements
    const smallGeometry = new THREE.OctahedronGeometry(0.5);
    const smallMaterial = new THREE.MeshBasicMaterial({ 
      color: 0xFF6600,
      transparent: true,
      opacity: 0.2
    });
    
    for (let i = 0; i < 8; i++) {
      const smallMesh = new THREE.Mesh(smallGeometry, smallMaterial);
      const angle = (i / 8) * Math.PI * 2;
      smallMesh.position.x = Math.cos(angle) * 4;
      smallMesh.position.y = Math.sin(angle) * 4;
      smallMesh.position.z = Math.sin(angle * 2) * 2;
      scene.add(smallMesh);
    }
    
    camera.position.z = 8;
    
    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;
    cameraRef.current = camera;
    meshRef.current = mesh;
    
    // Animation loop
    const animate = () => {
      animationRef.current = requestAnimationFrame(animate);
      
      if (!isAnimationPaused && meshRef.current) {
        meshRef.current.rotation.x += 0.005;
        meshRef.current.rotation.y += 0.01;
        
        // React to mouse position
        meshRef.current.position.x = (mousePosition.x - 0.5) * 0.5;
        meshRef.current.position.y = (mousePosition.y - 0.5) * -0.5;
        
        // React to scroll
        meshRef.current.scale.set(
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001,
          1 + scrollY * 0.0001
        );
        
        // Animate small meshes
        scene.children.forEach((child, index) => {
          if (child !== meshRef.current) {
            child.rotation.x += 0.02;
            child.rotation.y += 0.01;
            const time = Date.now() * 0.001;
            child.position.y += Math.sin(time + index) * 0.01;
          }
        });
      }
      
      renderer.render(scene, camera);
    };
    
    animate();
    
    // Handle resize
    const handleResize = () => {
      if (camera && renderer) {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [isAnimationPaused, mousePosition, scrollY]);

  // Mouse tracking
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX / window.innerWidth,
        y: e.clientY / window.innerHeight
      });
    };
    
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Layout renderers
  const renderModularGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-8">
      {scientificData.map((item, index) => (
        <div 
          key={item.id}
          className="bg-white border-2 border-black p-6 group hover:bg-black hover:text-white transition-all duration-300 cursor-pointer"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="flex items-center justify-between mb-4">
            <item.icon className="w-8 h-8 text-black group-hover:text-[#FF6600] transition-colors" />
            <div className={`w-3 h-3 rounded-full ${
              item.status === 'complete' ? 'bg-[#FF6600]' : 
              item.status === 'active' ? 'bg-black group-hover:bg-white' : 
              'bg-black/30 group-hover:bg-white/30'
            }`} />
          </div>
          <h3 className="text-xl font-bold mb-2">{item.title}</h3>
          <p className="text-sm mb-4 opacity-70">{item.field}</p>
          <div className="w-full bg-black/10 group-hover:bg-white/10 h-2">
            <div 
              className="h-full bg-[#FF6600] transition-all duration-1000"
              style={{ width: `${item.progress}%` }}
            />
          </div>
          <div className="mt-2 text-sm opacity-70">{item.progress}% Complete</div>
        </div>
      ))}
    </div>
  );

  const renderInfiniteScroll = () => (
    <div className="space-y-4 p-8">
      {[...Array(20)].map((_, index) => (
        <div 
          key={index}
          className="flex items-center space-x-6 p-4 border-b border-black/10 hover:bg-[#FF6600]/5 transition-all duration-300"
        >
          <div className="w-12 h-12 bg-black flex items-center justify-center text-white font-bold">
            {String(index + 1).padStart(2, '0')}
          </div>
          <div className="flex-1">
            <h4 className="font-bold">Research Entry {index + 1}</h4>
            <p className="text-sm opacity-70">Advanced scientific discovery and analysis</p>
          </div>
          <div className="w-8 h-8 bg-[#FF6600] rounded-full flex items-center justify-center">
            <ArrowRight className="w-4 h-4 text-white" />
          </div>
        </div>
      ))}
    </div>
  );

  const renderFullWidthCanvas = () => (
    <div className="relative h-[600px] bg-white border-4 border-black overflow-hidden">
      <div className="absolute inset-0 p-8">
        <div className="grid grid-cols-8 grid-rows-6 h-full gap-2">
          {[...Array(48)].map((_, index) => (
            <div 
              key={index}
              className={`${
                Math.random() > 0.7 ? 'bg-[#FF6600]' : 
                Math.random() > 0.5 ? 'bg-black' : 'bg-black/10'
              } transition-all duration-1000 hover:bg-[#FF6600] cursor-pointer`}
              style={{ 
                animationDelay: `${index * 20}ms`,
                transform: `scale(${0.8 + Math.random() * 0.4})`
              }}
            />
          ))}
        </div>
      </div>
      <div className="absolute top-4 left-4 bg-white p-4 border-2 border-black">
        <h3 className="font-bold mb-2">Data Visualization</h3>
        <p className="text-sm">Interactive scientific data matrix</p>
      </div>
    </div>
  );

  const renderFloatingConsole = () => (
    <div className="relative min-h-[600px] bg-white p-8">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-4xl font-bold mb-8">Scientific Instrument Control</h2>
        <div className="grid grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Activity className="w-16 h-16 text-black/50" />
            </div>
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Network className="w-16 h-16 text-black/50" />
            </div>
          </div>
          <div className="space-y-4">
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <Database className="w-16 h-16 text-black/50" />
            </div>
            <div className="h-32 bg-black/10 flex items-center justify-center">
              <GitBranch className="w-16 h-16 text-black/50" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating Control Panel */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 bg-white border-4 border-black p-6 w-80 z-10">
        <h3 className="font-bold mb-4">Control Panel</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-bold mb-2">Temperature</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-3/4 h-full bg-[#FF6600]" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-bold mb-2">Pressure</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-1/2 h-full bg-[#FF6600]" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-bold mb-2">Flow Rate</label>
            <div className="w-full bg-black/10 h-3">
              <div className="w-5/6 h-full bg-[#FF6600]" />
            </div>
          </div>
          <button className="w-full bg-[#FF6600] text-white font-bold py-3 hover:bg-black transition-colors">
            Execute Protocol
          </button>
        </div>
      </div>
    </div>
  );

  const renderScientificDashboard = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-8">
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white border-2 border-black p-6">
          <h3 className="font-bold mb-4">Experimental Progress</h3>
          <div className="h-40 bg-black/5 flex items-end justify-between p-4">
            {[40, 60, 35, 80, 55, 90, 75, 85].map((height, index) => (
              <div 
                key={index}
                className="bg-[#FF6600] w-8 transition-all duration-1000 hover:bg-black"
                style={{ height: `${height}%`, animationDelay: `${index * 100}ms` }}
              />
            ))}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-6">
          <div className="bg-white border-2 border-black p-6">
            <h4 className="font-bold mb-2">Active Experiments</h4>
            <div className="text-3xl font-bold text-[#FF6600]">24</div>
          </div>
          <div className="bg-white border-2 border-black p-6">
            <h4 className="font-bold mb-2">Success Rate</h4>
            <div className="text-3xl font-bold text-[#FF6600]">94.2%</div>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        <div className="bg-black text-white p-6">
          <h3 className="font-bold mb-4">System Status</h3>
          <div className="space-y-3">
            {['Quantum Processor', 'Neural Interface', 'Data Pipeline', 'Security Layer'].map((system, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm">{system}</span>
                <div className="w-3 h-3 bg-[#FF6600] rounded-full" />
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white border-2 border-black p-6">
          <h4 className="font-bold mb-4">Recent Results</h4>
          <div className="space-y-2">
            {scientificData.slice(0, 3).map((item) => (
              <div key={item.id} className="flex items-center space-x-3 text-sm">
                <item.icon className="w-4 h-4 text-[#FF6600]" />
                <span>{item.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderMultiColumnCards = () => (
    <div className="columns-1 md:columns-2 lg:columns-3 gap-6 p-8">
      {scientificData.map((item, index) => (
        <div 
          key={item.id}
          className="break-inside-avoid mb-6 bg-white border-2 border-black p-6 hover:bg-black hover:text-white group transition-all duration-300"
          style={{ animationDelay: `${index * 150}ms` }}
        >
          <item.icon className="w-12 h-12 text-[#FF6600] mb-4" />
          <h3 className="text-xl font-bold mb-3">{item.title}</h3>
          <p className="text-sm mb-4 opacity-70">{item.field}</p>
          <div className="space-y-2 mb-4">
            <div className="text-xs font-bold">PROGRESS ANALYSIS</div>
            <div className="w-full bg-black/10 group-hover:bg-white/10 h-1">
              <div 
                className="h-full bg-[#FF6600] transition-all duration-1000"
                style={{ width: `${item.progress}%` }}
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold">{item.progress}%</span>
            <ArrowRight className="w-4 h-4 opacity-50 group-hover:opacity-100" />
          </div>
        </div>
      ))}
    </div>
  );

  // Get current layout renderer
  const getCurrentLayoutRenderer = () => {
    switch (activeLayout) {
      case 'modular-grid':
        return renderModularGrid();
      case 'infinite-scroll':
        return renderInfiniteScroll();
      case 'full-width-canvas':
        return renderFullWidthCanvas();
      case 'floating-console':
        return renderFloatingConsole();
      case 'scientific-dashboard':
        return renderScientificDashboard();
      case 'multi-column-cards':
        return renderMultiColumnCards();
      default:
        return renderModularGrid();
    }
  };

  // Filter and sort layouts
  const filteredLayouts = Object.entries(layouts).filter(([key, layout]) => {
    const matchesCategory = activeCategory === 'all' || layout.category === activeCategory;
    const matchesSearch =
      layout.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      layout.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const sortedLayouts = filteredLayouts.sort((a, b) => {
    const [, layoutA] = a;
    const [, layoutB] = b;

    switch (sortBy) {
      case 'name':
        return layoutA.name.localeCompare(layoutB.name);
      case 'complexity':
        const complexityOrder = { 'Medium': 1, 'Advanced': 2, 'Expert': 3 };
        return complexityOrder[layoutA.complexity] - complexityOrder[layoutB.complexity];
      case 'category':
        return layoutA.category.localeCompare(layoutB.category);
      case 'updated':
        return new Date(layoutB.lastUpdated).getTime() - new Date(layoutA.lastUpdated).getTime();
      default:
        return 0;
    }
  });

  // Modular Card Component
  const LayoutCard = ({ layoutKey, layout }) => (
    <div 
      className={`group bg-white border-2 border-black p-6 cursor-pointer transition-all duration-300 hover:bg-black hover:text-white ${
        activeLayout === layoutKey ? 'bg-black text-white' : ''
      }`}
      onClick={() => setActiveLayout(layoutKey)}
    >
      <div className="flex items-center justify-between mb-4">
        <layout.icon className={`w-8 h-8 ${
          activeLayout === layoutKey ? 'text-[#FF6600]' : 'text-black group-hover:text-[#FF6600]'
        } transition-colors`} />
        <div className={`px-2 py-1 text-xs font-bold border ${
          activeLayout === layoutKey ? 'border-white text-white' : 'border-black text-black group-hover:border-white group-hover:text-white'
        }`}>
          {layout.complexity}
        </div>
      </div>
      <h3 className="text-xl font-bold mb-2">{layout.name}</h3>
      <p className="text-sm mb-4 opacity-70">{layout.description}</p>
      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-xs">
          <span>Speed</span>
          <span>{layout.performance.speed}%</span>
        </div>
        <div className={`w-full h-1 ${
          activeLayout === layoutKey ? 'bg-white/20' : 'bg-black/10 group-hover:bg-white/10'
        }`}>
          <div 
            className="h-full bg-[#FF6600] transition-all duration-1000"
            style={{ width: `${layout.performance.speed}%` }}
          />
        </div>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-xs opacity-70">Updated {layout.lastUpdated}</span>
        <div className="flex space-x-2">
          <button className="p-1 hover:bg-[#FF6600] hover:text-white transition-colors">
            <Eye className="w-4 h-4" />
          </button>
          <button className="p-1 hover:bg-[#FF6600] hover:text-white transition-colors">
            <Code className="w-4 h-4" />
          </button>
          <button className="p-1 hover:bg-[#FF6600] hover:text-white transition-colors">
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white text-black relative overflow-hidden">
      {/* 3D Background */}
      <div 
        ref={mountRef} 
        className="fixed inset-0 pointer-events-none z-0"
        style={{ opacity: 0.3 }}
      />
      
      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b-4 border-black bg-white">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-black flex items-center justify-center">
                  <Grid className="w-6 h-6 text-[#FF6600]" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">SciViz</h1>
                  <p className="text-sm opacity-70">Design System</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* 3D Animation Controls */}
                <div className="flex items-center space-x-2 border-2 border-black p-2">
                  <button
                    onClick={() => setIsAnimationPaused(!isAnimationPaused)}
                    className="p-2 hover:bg-black hover:text-white transition-colors"
                  >
                    {isAnimationPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  </button>
                  <button className="p-2 hover:bg-black hover:text-white transition-colors">
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>
                
                {/* Device Preview */}
                <div className="flex items-center space-x-2 border-2 border-black">
                  {[
                    { id: 'desktop', icon: Monitor },
                    { id: 'tablet', icon: Tablet },
                    { id: 'mobile', icon: Smartphone }
                  ].map(({ id, icon: Icon }) => (
                    <button
                      key={id}
                      onClick={() => setSelectedDevice(id)}
                      className={`p-2 transition-colors ${
                        selectedDevice === id ? 'bg-black text-white' : 'hover:bg-black hover:text-white'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                    </button>
                  ))}
                </div>
                
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="p-2 border-2 border-black hover:bg-black hover:text-white transition-colors"
                >
                  {isMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-6">
          <div className="max-w-7xl mx-auto text-center">
            <div className="mb-8">
              <div className="inline-flex items-center space-x-4 mb-6">
                {[...Array(5)].map((_, i) => (
                  <div 
                    key={i}
                    className="w-4 h-4 bg-[#FF6600] transform rotate-45"
                    style={{ animationDelay: `${i * 200}ms` }}
                  />
                ))}
              </div>
            </div>
            <h1 className="text-6xl md:text-8xl font-bold mb-6">
              Scientific
              <br />
              Visualization
              <br />
              Design System
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-70">
              A futuristic, minimalist design system for scientific exploration and data
              visualization. Every pixel contributes to discovery.
            </p>
            <button className="bg-black text-white px-8 py-4 font-bold hover:bg-[#FF6600] transition-colors">
              View Documentation
            </button>
          </div>
        </section>

        {/* Layout Systems Section */}
        <section className="py-20 px-6 border-t-4 border-black">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4">Layout Systems</h2>
              <p className="text-xl opacity-70 max-w-3xl mx-auto">
                Choose from multiple innovative layout patterns, each designed for
                different scientific visualization needs.
              </p>
            </div>

            {/* Controls */}
            <div className="flex flex-wrap items-center justify-between mb-8 gap-4">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-50" />
                  <input
                    type="text"
                    placeholder="Search layouts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border-2 border-black focus:outline-none focus:border-[#FF6600]"
                  />
                </div>
                
                <select
                  value={activeCategory}
                  onChange={(e) => setActiveCategory(e.target.value)}
                  className="px-4 py-2 border-2 border-black focus:outline-none focus:border-[#FF6600]"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center space-x-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border-2 border-black focus:outline-none focus:border-[#FF6600]"
                >
                  <option value="name">Sort by Name</option>
                  <option value="complexity">Sort by Complexity</option>
                  <option value="category">Sort by Category</option>
                  <option value="updated">Sort by Updated</option>
                </select>
                
                <div className="flex border-2 border-black">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors ${
                      viewMode === 'grid' ? 'bg-black text-white' : 'hover:bg-black hover:text-white'
                    }`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors ${
                      viewMode === 'list' ? 'bg-black text-white' : 'hover:bg-black hover:text-white'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Layout Grid */}
            <div className={`grid gap-6 mb-12 ${
              viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'
            }`}>
              {sortedLayouts.map(([key, layout]) => (
                <LayoutCard key={key} layoutKey={key} layout={layout} />
              ))}
            </div>

            {/* Preview Section */}
            <div className="border-4 border-black bg-white">
              <div className="border-b-2 border-black p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-xl font-bold">{layouts[activeLayout]?.name}</h3>
                  <span className="text-sm opacity-70">{layouts[activeLayout]?.category}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setIsPreviewFullscreen(!isPreviewFullscreen)}
                    className="p-2 hover:bg-black hover:text-white transition-colors"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className={`${
                isPreviewFullscreen ? 'fixed inset-0 z-50 bg-white' : 'min-h-[600px]'
              }`}>
                {isPreviewFullscreen && (
                  <div className="absolute top-4 right-4 z-10">
                    <button
                      onClick={() => setIsPreviewFullscreen(false)}
                      className="p-2 bg-black text-white hover:bg-[#FF6600] transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )}
                {getCurrentLayoutRenderer()}
              </div>
            </div>
          </div>
        </section>

        {/* System Features */}
        <section className="py-20 px-6 border-t-4 border-black bg-black text-white">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4">System Features</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-8">
                <Zap className="w-16 h-16 text-[#FF6600] mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Lightning Fast</h3>
                <p className="opacity-70">Optimized for scientific computing performance</p>
              </div>
              
              <div className="text-center p-8">
                <Atom className="w-16 h-16 text-[#FF6600] mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Scientific Accuracy</h3>
                <p className="opacity-70">Precise visualizations with mathematical integrity</p>
              </div>
              
              <div className="text-center p-8">
                <Network className="w-16 h-16 text-[#FF6600] mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Modular System</h3>
                <p className="opacity-70">Flexible components for any research need</p>
              </div>
            </div>
          </div>
        </section>

        {/* Start Building Section */}
        <section className="py-20 px-6 border-t-4 border-black">
          <div className="max-w-7xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-4">Start Building</h2>
            <p className="text-xl mb-8 opacity-70">
              Download the complete SciViz Design System and start creating beautiful
              scientific interfaces today.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="border-2 border-black p-6">
                <h3 className="text-xl font-bold mb-4">Components</h3>
                <p className="text-sm mb-4 opacity-70">Complete UI component library</p>
                <button className="w-full bg-black text-white py-3 font-bold hover:bg-[#FF6600] transition-colors">
                  View Components
                </button>
              </div>
              
              <div className="border-2 border-black p-6">
                <h3 className="text-xl font-bold mb-4">Templates</h3>
                <p className="text-sm mb-4 opacity-70">Ready-to-use layout templates</p>
                <button className="w-full bg-black text-white py-3 font-bold hover:bg-[#FF6600] transition-colors">
                  Browse Templates
                </button>
              </div>
              
              <div className="border-2 border-black p-6">
                <h3 className="text-xl font-bold mb-4">Documentation</h3>
                <p className="text-sm mb-4 opacity-70">Complete implementation guide</p>
                <button className="w-full bg-black text-white py-3 font-bold hover:bg-[#FF6600] transition-colors">
                  Read Docs
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default SciVizAwwwardsUnified;

